import { createClient } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseServiceRoleKey = import.meta.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceRoleKey) {
  throw new Error('Missing Supabase environment variables for admin client');
}

// Admin client with service role key - bypasses RLS
export const supabaseAdmin = createClient(supabaseUrl, supabaseServiceRoleKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Admin database service functions
export const adminDbService = {
  // Deposit functions
  async confirmDeposit(depositId, depositAmount, userId) {
    try {
      // Start a transaction-like operation

      // 1. Get deposit details first to get cryptocurrency info
      const { data: depositData, error: depositFetchError } = await supabaseAdmin
        .from('crypto_deposits')
        .select('cryptocurrency')
        .eq('id', depositId)
        .single();

      if (depositFetchError) throw depositFetchError;

      // 2. Update deposit status
      const { error: depositError } = await supabaseAdmin
        .from('crypto_deposits')
        .update({
          status: 'confirmed',
          confirmed_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', depositId);

      if (depositError) throw depositError;

      // 3. Get current user data (including auth_id)
      const { data: userData, error: fetchError } = await supabaseAdmin
        .from('users')
        .select('balance, auth_id')
        .eq('id', userId)
        .single();

      if (fetchError) throw fetchError;

      const currentBalance = userData?.balance || 0;
      const newBalance = currentBalance + depositAmount;

      // 4. Update user balance
      const { error: balanceError } = await supabaseAdmin
        .from('users')
        .update({
          balance: newBalance,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);

      if (balanceError) throw balanceError;

      // 5. Create transaction record using auth_id (to match Dashboard queries)
      const cryptoName = depositData.cryptocurrency === 'BTC' ? 'Bitcoin' : depositData.cryptocurrency;
      const { error: transactionError } = await supabaseAdmin
        .from('transactions')
        .insert({
          user_id: userData.auth_id, // Use auth_id instead of id
          type: 'deposit',
          amount: depositAmount,
          status: 'completed',
          description: `${cryptoName} Deposit`,
          created_at: new Date().toISOString()
        });

      if (transactionError) throw transactionError;

      return { success: true };

    } catch (error) {
      console.error('Error in confirmDeposit:', error);
      throw error;
    }
  },

  // Reject deposit
  async rejectDeposit(depositId) {
    try {
      const { error } = await supabaseAdmin
        .from('crypto_deposits')
        .update({
          status: 'failed',
          updated_at: new Date().toISOString()
        })
        .eq('id', depositId);

      if (error) throw error;
      return { success: true };

    } catch (error) {
      console.error('Error in rejectDeposit:', error);
      throw error;
    }
  },

  // Get all deposits (admin view)
  async getAllDeposits(limit = 50) {
    try {
      const { data, error } = await supabaseAdmin
        .from('crypto_deposits')
        .select(`
          *,
          users (
            name,
            email
          )
        `)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) throw error;
      return data || [];

    } catch (error) {
      console.error('Error in getAllDeposits:', error);
      throw error;
    }
  },

  // Get all users (admin view)
  async getAllUsers(limit = 100) {
    try {
      const { data, error } = await supabaseAdmin
        .from('users')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) throw error;
      return data || [];

    } catch (error) {
      console.error('Error in getAllUsers:', error);
      throw error;
    }
  },

  // Get all transactions (admin view)
  async getAllTransactions(limit = 100) {
    try {
      const { data, error } = await supabaseAdmin
        .from('transactions')
        .select(`
          *,
          users (
            name,
            email
          )
        `)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) throw error;
      return data || [];

    } catch (error) {
      console.error('Error in getAllTransactions:', error);
      throw error;
    }
  },

  // Update user balance (admin function)
  async updateUserBalance(userId, newBalance) {
    try {
      const { error } = await supabaseAdmin
        .from('users')
        .update({
          balance: newBalance,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);

      if (error) throw error;
      return { success: true };

    } catch (error) {
      console.error('Error in updateUserBalance:', error);
      throw error;
    }
  }
};
