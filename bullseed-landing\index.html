<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>BullSeed - Crypto Investment Platform</title>

    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json" />

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#00d4aa" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    <meta name="apple-mobile-web-app-title" content="BullSeed Admin" />

    <!-- iOS Icons -->
    <link rel="apple-touch-icon" href="/logo192.png" />
    <link rel="apple-touch-icon" sizes="152x152" href="/logo192.png" />
    <link rel="apple-touch-icon" sizes="180x180" href="/logo192.png" />
    <link rel="apple-touch-icon" sizes="167x167" href="/logo192.png" />

    <!-- Microsoft Tiles -->
    <meta name="msapplication-TileColor" content="#00d4aa" />
    <meta name="msapplication-TileImage" content="/logo192.png" />
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>

    <!-- Service Worker Registration -->
    <script>
      if ('serviceWorker' in navigator) {
        window.addEventListener('load', () => {
          navigator.serviceWorker.register('/sw.js')
            .then((registration) => {
              console.log('SW registered: ', registration);
            })
            .catch((registrationError) => {
              console.log('SW registration failed: ', registrationError);
            });
        });
      }
    </script>
  </body>
</html>
