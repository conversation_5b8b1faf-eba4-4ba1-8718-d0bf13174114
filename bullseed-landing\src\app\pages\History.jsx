import React, { useState, useEffect } from 'react';
import dbService from '../../services/dbService';
import { supabase } from '../../lib/supabase';
import '../styles/History.css';

const History = ({ user }) => {
  const [transactions, setTransactions] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchTransactions = async () => {
      if (!user?.id) {
        setLoading(false);
        return;
      }

      try {
        const authId = user.auth_id || user.id;
        const userTransactions = await dbService.getTransactions(authId, 50);
        setTransactions(userTransactions || []);
      } catch (error) {
        console.error('Error fetching transactions:', error);
        setTransactions([]);
      } finally {
        setLoading(false);
      }
    };

    fetchTransactions();

    // Set up real-time subscription for new transactions
    if (user?.id) {
      const authId = user.auth_id || user.id;

      const subscription = supabase
        .channel('transaction_history')
        .on('postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'transactions',
            filter: `user_id=eq.${authId}`
          },
          (payload) => {
            console.log('History - New transaction:', payload);
            fetchTransactions(); // Refresh transactions
          }
        )
        .subscribe();

      return () => {
        subscription.unsubscribe();
      };
    }
  }, [user]);

  return (
    <div className="history">
      <div className="history-header">
        <h1>Transaction History</h1>
        <p>View all your transaction history and account activity</p>
      </div>

      <div className="history-content">
        {loading ? (
          <div className="history-loading">
            <div className="loading-spinner"></div>
            <span>Loading transactions...</span>
          </div>
        ) : (
          <div className="transaction-history-table">
            <div className="transaction-history-header">
              <div className="transaction-col">TYPE</div>
              <div className="transaction-col">DESCRIPTION</div>
              <div className="transaction-col">DATE</div>
              <div className="transaction-col">AMOUNT</div>
              <div className="transaction-col">STATUS</div>
            </div>
            <div className="transaction-history-body">
              {transactions.length === 0 ? (
                <div className="transaction-history-empty">
                  <p>No transactions found</p>
                </div>
              ) : (
                transactions.map((transaction) => (
                  <div key={transaction.id} className="transaction-history-row">
                    <div className="transaction-col">
                      <div className={`transaction-type ${transaction.type}`}>
                        {transaction.type.charAt(0).toUpperCase() + transaction.type.slice(1)}
                      </div>
                    </div>
                    <div className="transaction-col">
                      <span>{transaction.description || `${transaction.type} transaction`}</span>
                    </div>
                    <div className="transaction-col">
                      {new Date(transaction.created_at).toLocaleDateString('en-US', {
                        day: '2-digit',
                        month: 'short',
                        year: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </div>
                    <div className="transaction-col">
                      <span className={`transaction-amount ${transaction.type === 'deposit' ? 'positive' : 'negative'}`}>
                        {transaction.type === 'deposit' ? '+' : '-'}${parseFloat(transaction.amount).toLocaleString()}
                      </span>
                    </div>
                    <div className="transaction-col">
                      <span className={`transaction-status ${transaction.status}`}>
                        {transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1)}
                      </span>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default History;
