/* History Page Styles */
.history {
  padding: 32px;
  max-width: 1400px;
  margin: 0 auto;
}

.history-header {
  margin-bottom: 32px;
}

.history-header h1 {
  font-size: 32px;
  font-weight: 700;
  color: white;
  margin-bottom: 8px;
}

.history-header p {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.7);
}

/* Statistics Cards */
.history-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.history-stat-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  padding: 24px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.3s ease;
}

.history-stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
  border-color: rgba(255, 255, 255, 0.2);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  flex-shrink: 0;
}

.deposit-icon {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.2), rgba(5, 150, 105, 0.2));
  border: 1px solid rgba(16, 185, 129, 0.4);
}

.investment-icon {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(37, 99, 235, 0.2));
  border: 1px solid rgba(59, 130, 246, 0.4);
}

.earnings-icon {
  background: linear-gradient(135deg, rgba(168, 85, 247, 0.2), rgba(147, 51, 234, 0.2));
  border: 1px solid rgba(168, 85, 247, 0.4);
}

.growth-icon {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.2), rgba(217, 119, 6, 0.2));
  border: 1px solid rgba(245, 158, 11, 0.4);
}

.stat-content {
  flex: 1;
}

.stat-label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 4px;
  font-weight: 500;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: white;
}

/* Date Filter Controls */
.history-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 32px;
}

.date-filter {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  padding: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.date-filter-buttons {
  display: flex;
  gap: 4px;
}

.filter-btn {
  padding: 12px 24px;
  border: none;
  background: transparent;
  color: rgba(255, 255, 255, 0.7);
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.filter-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.filter-btn.active {
  background: linear-gradient(135deg, #00d4aa, #00b894);
  color: white;
  box-shadow: 0 4px 16px rgba(0, 212, 170, 0.3);
}

.custom-date-picker {
  margin-top: 16px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.date-inputs {
  display: flex;
  gap: 16px;
  align-items: end;
}

.date-input-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.date-input-group label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
}

.date-input {
  padding: 12px 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.05);
  color: white;
  font-size: 14px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.date-input:focus {
  outline: none;
  border-color: #00d4aa;
  box-shadow: 0 0 0 3px rgba(0, 212, 170, 0.1);
}

.date-input::-webkit-calendar-picker-indicator {
  filter: invert(1);
  cursor: pointer;
}

.history-tabs {
  display: flex;
  gap: 4px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 4px;
}

.history-tab {
  display: flex;
  align-items: center;
  gap: 8px;
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  padding: 12px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.history-tab:hover {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.8);
}

.history-tab.active {
  background: rgba(0, 212, 170, 0.2);
  color: #00d4aa;
}

.history-filters {
  display: flex;
  gap: 12px;
  align-items: center;
}

.date-filter {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
}

.export-btn {
  background: rgba(0, 212, 170, 0.1);
  border: 1px solid rgba(0, 212, 170, 0.3);
  color: #00d4aa;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.export-btn:hover {
  background: rgba(0, 212, 170, 0.15);
}

.history-content {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  overflow: hidden;
}

.history-table {
  width: 100%;
}

.history-table-header {
  display: grid;
  gap: 16px;
  padding: 20px 24px;
  background: rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.history-table-header .history-col {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  font-weight: 600;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

.history-table-body {
  display: flex;
  flex-direction: column;
}

.history-table-row {
  display: grid;
  gap: 16px;
  padding: 20px 24px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  align-items: center;
  transition: all 0.3s ease;
}

.history-table-row:hover {
  background: rgba(255, 255, 255, 0.02);
}

.history-table-row:last-child {
  border-bottom: none;
}

/* Investment History Grid */
.history-table-header:has(.investment-plan),
.history-table-row:has(.investment-plan) {
  grid-template-columns: 2fr 1fr 1fr 1fr 2fr 1fr 80px;
}

/* Transaction History Grid */
.history-table-header:has(.transaction-type),
.history-table-row:has(.transaction-type) {
  grid-template-columns: 1.5fr 1fr 1fr 1fr 1fr 2fr;
}

/* Referral History Grid */
.history-table-header:has(.referred-user),
.history-table-row:has(.referred-user) {
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
}

.history-col {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
}

.investment-plan {
  display: flex;
  flex-direction: column;
}

.plan-name {
  font-weight: 600;
  color: white;
  margin-bottom: 4px;
}

.amount {
  font-weight: 700;
  color: white;
}

.daily-return,
.total-return {
  font-weight: 600;
  color: #00d4aa;
}

.period {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.period-separator {
  color: rgba(255, 255, 255, 0.4);
}

.status-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.days-remaining {
  font-size: 10px;
  opacity: 0.8;
}

.action-btn {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.transaction-type {
  display: flex;
  align-items: center;
  gap: 12px;
}

.transaction-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.transaction-icon.deposit {
  background: rgba(0, 212, 170, 0.2);
  color: #00d4aa;
}

.transaction-icon.withdrawal {
  background: rgba(245, 158, 11, 0.2);
  color: #f59e0b;
}

.transaction-type-text {
  font-weight: 600;
  color: white;
}

.currency {
  font-weight: 600;
  color: rgba(255, 255, 255, 0.8);
}

.date {
  color: rgba(255, 255, 255, 0.7);
}

.tx-hash {
  display: flex;
  align-items: center;
  gap: 8px;
}

.tx-hash-text {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.copy-hash-btn {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.4);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.copy-hash-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.6);
}

.referred-user {
  font-weight: 600;
  color: white;
}

.commission {
  font-weight: 700;
  color: #00d4aa;
}

.level {
  background: rgba(0, 212, 170, 0.1);
  color: #00d4aa;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

/* Mobile Responsive */
@media (max-width: 1024px) {
  .history-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }
  
  .history-tabs {
    justify-content: center;
  }
  
  .history-filters {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .history {
    padding: 16px;
  }
  
  .history-header h1 {
    font-size: 24px;
  }
  
  .history-tabs {
    flex-direction: column;
    gap: 8px;
  }
  
  .history-tab {
    justify-content: center;
  }
  
  .history-table-header {
    display: none;
  }
  
  .history-table-row {
    grid-template-columns: 1fr !important;
    gap: 12px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    margin-bottom: 12px;
    border-bottom: none;
  }
  
  .history-col {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  }
  
  .history-col:last-child {
    border-bottom: none;
  }
  
  .history-col::before {
    content: attr(data-label);
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
  
  .investment-plan,
  .transaction-type {
    flex-direction: row;
    justify-content: flex-end;
  }
  
  .period {
    justify-content: flex-end;
  }
  
  .tx-hash {
    justify-content: flex-end;
  }
}

/* New Transaction History Table Styles */
.history-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 60px 20px;
  color: rgba(255, 255, 255, 0.7);
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid #00d4aa;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.transaction-history-table {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  max-width: 100%;
}

.transaction-history-header {
  display: grid;
  grid-template-columns: 100px 1fr 160px 120px 100px;
  gap: 12px;
  padding: 12px 20px;
  background: rgba(255, 255, 255, 0.08);
  border-bottom: 2px solid rgba(255, 255, 255, 0.1);
}

.transaction-history-header .transaction-col {
  font-size: 13px;
  font-weight: 700;
  color: rgba(255, 255, 255, 0.9);
  text-transform: uppercase;
  letter-spacing: 1px;
}

.transaction-history-body {
  max-height: 500px;
  overflow-y: auto;
}

.transaction-history-body::-webkit-scrollbar {
  width: 6px;
}

.transaction-history-body::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

.transaction-history-body::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

.transaction-history-body::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

.transaction-history-row {
  display: grid;
  grid-template-columns: 100px 1fr 160px 120px 100px;
  gap: 12px;
  padding: 8px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
  align-items: center;
  min-height: 48px;
  max-height: 48px;
}

.transaction-history-row:hover {
  background: rgba(255, 255, 255, 0.03);
  transform: translateX(2px);
}

.transaction-history-row:last-child {
  border-bottom: none;
}

.transaction-col {
  display: flex;
  align-items: center;
  color: white;
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
  height: 100%;
}

.transaction-type {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 16px;
  font-size: 10px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  min-width: 60px;
  justify-content: center;
  white-space: nowrap;
  height: 24px;
}

.transaction-type.deposit {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.2), rgba(5, 150, 105, 0.2));
  color: #10b981;
  border: 1px solid rgba(16, 185, 129, 0.4);
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.1);
}

.transaction-type.investment {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(37, 99, 235, 0.2));
  color: #3b82f6;
  border: 1px solid rgba(59, 130, 246, 0.4);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

.transaction-type.withdrawal {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.2), rgba(220, 38, 38, 0.2));
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.4);
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.1);
}

.transaction-type.referral {
  background: linear-gradient(135deg, rgba(168, 85, 247, 0.2), rgba(147, 51, 234, 0.2));
  color: #a855f7;
  border: 1px solid rgba(168, 85, 247, 0.4);
  box-shadow: 0 2px 8px rgba(168, 85, 247, 0.1);
}

.transaction-type.earning {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.2), rgba(217, 119, 6, 0.2));
  color: #f59e0b;
  border: 1px solid rgba(245, 158, 11, 0.4);
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.1);
}

.transaction-amount {
  font-weight: 700;
  font-size: 14px;
}

.transaction-amount.positive {
  color: #10b981;
}

.transaction-amount.negative {
  color: #ef4444;
}

.transaction-status {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 16px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  min-width: 60px;
  justify-content: center;
  height: 24px;
}

.transaction-status.completed {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.2), rgba(5, 150, 105, 0.2));
  color: #10b981;
  border: 1px solid rgba(16, 185, 129, 0.4);
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.1);
}

.transaction-status.pending {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.2), rgba(217, 119, 6, 0.2));
  color: #f59e0b;
  border: 1px solid rgba(245, 158, 11, 0.4);
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.1);
}

.transaction-status.failed {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.2), rgba(220, 38, 38, 0.2));
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.4);
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.1);
}

.transaction-history-empty {
  padding: 60px 20px;
  text-align: center;
  color: rgba(255, 255, 255, 0.5);
}

.transaction-history-empty p {
  font-size: 16px;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .history {
    padding: 16px;
  }

  .history-stats {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .history-stat-card {
    padding: 20px;
  }

  .stat-icon {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }

  .stat-value {
    font-size: 20px;
  }

  .date-filter-buttons {
    flex-wrap: wrap;
    gap: 8px;
  }

  .filter-btn {
    padding: 10px 16px;
    font-size: 13px;
  }

  .date-inputs {
    flex-direction: column;
    gap: 12px;
  }

  .transaction-history-header,
  .transaction-history-row {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .transaction-history-header .transaction-col {
    display: none;
  }

  .transaction-history-row {
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 8px;
    background: rgba(255, 255, 255, 0.02);
  }

  .transaction-col {
    justify-content: space-between;
    padding: 4px 0;
  }

  .transaction-col::before {
    content: attr(data-label);
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
    text-transform: uppercase;
    font-weight: 600;
  }
}

@media (max-width: 480px) {
  .history-header h1 {
    font-size: 24px;
  }

  .history-stat-card {
    padding: 16px;
  }

  .stat-value {
    font-size: 18px;
  }

  .filter-btn {
    padding: 8px 12px;
    font-size: 12px;
  }
}
